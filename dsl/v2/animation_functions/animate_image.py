"""
effect: |
    在Manim场景中显示图像，支持缩放平移动画和注释文本。

use_cases:
    - 展示需要详细解释的图片或截图
    - 展示图表、图解或可视化内容
    - 显示产品或界面截图并添加注释说明

params:
  scene:
    type: FeynmanScene
    desc: Man<PERSON>场景实例（由系统自动传入）
  image_path:
    type: str
    desc: 要显示的图片的本地文件路径
    required: true
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None
  narration:
    type: str
    desc: 在图片显示时播放的语音旁白文本
    required: true
  annotation:
    type: str或List[str]
    desc: 作为注释显示在图片旁边的文本，支持Markdown格式
    default: None

dsl_examples:
    - type: animate_image
      params:
        image_path: assets/manim_logo.png
        narration: 让我们看看这张图片。
        annotation: 这是一张示例图片，展示了重要的内容。
    - type: animate_image
      params:
        image_path: assets/manim_logo.png
        id: architecture_diagram
        annotation: |
            ## 系统架构
            - 前端组件
            - 后端服务
            - 数据存储
        narration: 这张架构图展示了系统的主要组件和它们之间的关系。

notes:
    - 图片文件必须存在且路径正确，否则会抛出FileNotFoundError
    - 图片会自动缩放以适应场景，并根据图片比例可能会添加平移动画
    - 如果提供annotation，它会作为文本显示在图片右侧
"""

import os
from typing import TYPE_CHECKING, Any, Optional

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene

import numpy as np
from loguru import logger
from manim import *

from dsl.v2.animation_functions.animate_markdown import _create_markdown_mobjects
from dsl.v2.themes.theme_utils import ThemeUtils
from utils.md_to_pango import MarkdownToSimplifiedConverter


# ---- 辅助函数 ----
def _create_image_mobject(image_path: str, **kwargs) -> ImageMobject | SVGMobject:
    """
    创建图像 Manim 对象。

    Args:
        image_path: 图像文件路径
        **kwargs: 其他 ImageMobject 参数

    Returns:
        ImageMobject | SVGMobject: Manim ImageMobject 对象或 SVGMobject 对象
    """
    if not os.path.exists(image_path):
        logger.error(f"图像文件未找到: {image_path}")
        raise FileNotFoundError(f"图像文件未找到: {image_path}")

    return SVGMobject(image_path, **kwargs) if image_path.endswith(".svg") else ImageMobject(image_path, **kwargs)


def _calculate_pan_animation_params(obj: Mobject, target_rect: Rectangle) -> dict[str, Any]:
    """
    计算图像平移动画的参数。

    Args:
        obj: 要平移的对象
        target_rect: 目标区域的矩形

    Returns:
        Dict: 包含平移动画参数的字典
    """
    params = {}

    # 获取原始尺寸
    orig_width = obj.width
    orig_height = obj.height

    # 获取目标区域尺寸
    target_width = target_rect.width
    target_height = target_rect.height

    # 目标区域中心
    target_center = target_rect.get_center()

    # 检查宽高比
    image_aspect_ratio = orig_width / orig_height
    if image_aspect_ratio > 1.5:  # 宽图像 -> 向左平移
        target_height_scaled = target_height * 0.95
        scale_factor = target_height_scaled / orig_height if orig_height > 0 else 1.0
        obj.scale(scale_factor)
        scaled_width = obj.width

        # Initial position: Center of image is at target_right_edge + scaled_width / 2
        # (So, left edge of image is at target_right_edge)
        initial_pos_x = target_center[0] + (scaled_width / 4)
        initial_pos_y = target_center[1]
        initial_pos = np.array([initial_pos_x, initial_pos_y, target_center[2]])

        shift_dist = scaled_width / 2  # Pan half width
        pan_direction = LEFT
    elif image_aspect_ratio < 0.6:  # 长图像 -> 向上平移
        target_width_scaled = target_width * 0.95
        scale_factor = target_width_scaled / orig_width if orig_width > 0 else 1.0
        obj.scale(scale_factor)
        scaled_height = obj.height

        # Initial position: Center of image is at target_bottom_edge - scaled_height / 2
        # (So, top edge of image is at target_bottom_edge)
        initial_pos_x = target_center[0]
        initial_pos_y = target_center[1] - (scaled_height / 4)
        initial_pos = np.array([initial_pos_x, initial_pos_y, target_center[2]])

        shift_dist = scaled_height / 2  # Pan half height
        pan_direction = UP
    else:
        # 不需要平移
        initial_pos = target_center
        shift_dist = 0
        pan_direction = None

    params["initial_pos"] = initial_pos
    params["shift_distance"] = shift_dist
    params["pan_direction"] = pan_direction
    params["target_rect"] = target_rect

    return params


def _apply_standard_positioning(obj: Mobject, target_rect: Rectangle) -> None:
    """
    应用标准定位和缩放。

    Args:
        obj: 要定位的对象
        target_rect: 目标区域的矩形
    """
    # 定位到区域中心
    obj.move_to(target_rect.get_center())

    # 缩放以适应区域（带边距）
    target_width = target_rect.width * 0.9
    target_height = target_rect.height * 0.9
    current_width = obj.width
    current_height = obj.height

    if current_width > 0 and current_height > 0:
        scale_w = target_width / current_width
        scale_h = target_height / current_height
        scale_factor = min(scale_w, scale_h, 1.0)
        if scale_factor < 1.0:
            obj.scale(scale_factor)


def _create_and_position_annotation_text(annotation: str, target_rect: Rectangle, full_width: bool = False) -> Mobject:
    """
    创建和定位注释文本。

    Args:
        annotation: 注释文本内容
        target_rect: 目标区域的矩形
        full_width: 是否使用全宽(True)或半宽(False)

    Returns:
        创建的文本对象
    """
    if not annotation:
        return None

    target_width = target_rect.width
    target_height = target_rect.height
    target_center = target_rect.get_center()

    # 创建文本对象
    converter = MarkdownToSimplifiedConverter()
    text_obj = _create_markdown_mobjects(converter.convert(annotation))

    # 缩放以适应宽度
    width_factor = (
        ThemeUtils.get_component_style("annotation", "full_width_factor", 0.7)
        if full_width
        else ThemeUtils.get_component_style("annotation", "half_width_factor", 0.45)
    )
    if text_obj.width > target_width * width_factor:
        text_obj.scale_to_fit_width(target_width * width_factor)

    # 缩放以适应高度
    height_factor = ThemeUtils.get_component_style("annotation", "height_factor", 0.8)
    if text_obj.height > target_height * height_factor:
        text_obj.scale_to_fit_height(target_height * height_factor)

    # 定位 - 基于full_width居中或放置在右半部分
    if full_width:
        text_obj.move_to(target_center)
    else:
        offset_ratio = ThemeUtils.get_component_style("annotation", "position_offset_ratio", 0.25)
        text_obj.move_to(target_center + RIGHT * (target_width * offset_ratio))

    return text_obj


def _animate_zoom_and_pan(
    scene: "FeynmanScene", obj: Mobject, narration: str, pan_params: dict[str, Any], annotation: Optional[str] = None
) -> Optional[Mobject]:
    """
    缩放和平移动画策略。

    Args:
        scene: Manim 场景
        obj: 要动画的对象
        narration: 旁白文本
        pan_params: 平移动画参数
        annotation: 图片注释文本（可选）
        reference_id: 内容的引用ID（可选）

    Returns:
        创建的注释文本对象（如果有）
    """
    text_obj = None

    with scene.voiceover(text=narration) as tracker:
        # 初始缩放
        target_rect = pan_params["target_rect"]
        target_width = obj.width
        obj.scale(1e-6).move_to(target_rect.get_center())
        width_scale_factor = ThemeUtils.get_component_style("image", "initial_scale_width", 0.7)
        height_scale_factor = ThemeUtils.get_component_style("image", "initial_scale_height", 0.7)
        scale_factor = min(
            target_rect.width * width_scale_factor / obj.width, target_rect.height * height_scale_factor / obj.height
        )
        scene.play(
            FadeIn(obj), obj.animate.scale(scale_factor), run_time=ThemeUtils.get_animation_duration("image_fade_in", 2)
        )

        # 条件平移
        if pan_params["shift_distance"] > 0.01:
            scene.play(
                obj.animate.move_to(pan_params["initial_pos"]).scale(target_width / obj.width),
                run_time=ThemeUtils.get_animation_duration("image_position", 1),
            )
            max_pan_duration = ThemeUtils.get_component_style("image", "max_pan_duration", 5.0)
            scene.play(
                obj.animate.shift(pan_params["pan_direction"] * pan_params["shift_distance"]),
                run_time=min(max_pan_duration, tracker.duration / 2),
                rate_func=linear,
            )
        else:
            enlarge_factor = min(target_rect.width / obj.width, target_rect.height / obj.height)
            scene.play(obj.animate.scale(enlarge_factor), run_time=ThemeUtils.get_animation_duration("image_scale", 1))
            max_wait_duration = ThemeUtils.get_component_style("image", "max_wait_duration", 5.0)
            scene.wait(min(max_wait_duration, tracker.duration / 2))

        # 如果有注释，显示注释
        if annotation:
            text_obj = _create_and_position_annotation_text(annotation, target_rect, full_width=False)

            # 移动图像到左半部分
            half_width_factor = ThemeUtils.get_component_style("image", "half_width_factor", 0.9)
            target_half_width = target_rect.width * half_width_factor / 2
            scale_w2 = min(1.0, target_half_width / obj.width)
            scale_h2 = min(1.0, target_rect.height * half_width_factor / obj.height)
            scale_factor2 = min(scale_w2, scale_h2)
            left_offset_ratio = ThemeUtils.get_component_style("image", "left_position_offset_ratio", 0.25)
            left_half_center = np.array(target_rect.get_center()) + LEFT * (target_rect.width * left_offset_ratio)

            scene.play(
                obj.animate.scale(scale_factor2).move_to(left_half_center),
                FadeIn(text_obj, shift=UP),
                run_time=ThemeUtils.get_animation_duration("image_reposition", 1),
            )
            wait_time = ThemeUtils.get_component_style("image", "annotation_wait_time", 2)
            scene.wait(wait_time)

    return text_obj


def _animate_annotation_only(
    scene: "FeynmanScene", annotation: str, target_rect: Rectangle, narration: Optional[str] = None
) -> Optional[Mobject]:
    """
    仅注释显示动画策略。

    Args:
        scene: Manim 场景
        annotation: 注释文本
        target_rect: 目标区域的矩形
        narration: 旁白文本（可选）

    Returns:
        创建的注释文本对象
    """
    text_obj = _create_and_position_annotation_text(annotation, target_rect, full_width=True)

    with scene.voiceover(text=narration) as tracker:
        lag_ratio = ThemeUtils.get_component_style("annotation", "lag_ratio", 0.3)
        max_animation_time = ThemeUtils.get_component_style("annotation", "max_animation_time", 2)
        scene.play(
            LaggedStart(
                *[FadeIn(obj, shift=RIGHT) for obj in text_obj.submobjects],
                lag_ratio=lag_ratio,
                run_time=min(max_animation_time, tracker.duration),
            )
        )
    return text_obj


# ---- 主函数 ----
def animate_image(
    scene: "FeynmanScene",
    image_path: str,
    id: Optional[str] = None,
    narration: Optional[str] = None,
    annotation: Optional[str | list[str]] = None,
    overlay_text: Optional[str | list[str]] = None,
) -> None:
    # 1. 初始化和基本设置
    # 创建唯一的引用 ID
    reference_id = id if id else f"image_{abs(hash(image_path)) % 10000}"

    logger.info(f"显示图片内容: region='full_screen', id='{reference_id}'")

    # 2. 获取目标区域并清除
    target_rect = scene.full_screen_rect
    scene.clear_current_mobj()

    if isinstance(overlay_text, list):
        annotation = "\n- ".join([""] + overlay_text)
    elif isinstance(overlay_text, str):
        annotation = overlay_text
    if isinstance(annotation, list):
        annotation = "\n- ".join([""] + annotation)

    # 3. 检查图像是否存在
    image_exists = os.path.exists(image_path)
    if not image_exists:
        logger.warning(f"图像文件未找到: {image_path}")
        if not annotation:
            logger.warning("没有提供注释文本，跳过显示。")
            return

        # 处理仅有注释的情况
        logger.info("图像不存在但有注释文本，将全屏显示注释。")
        try:
            text_obj = _animate_annotation_only(scene, annotation, target_rect, narration)
            scene.current_mobj = text_obj
        except Exception as e:
            logger.error(f"处理注释时出错: {e}")
        return

    # 4. 创建图像对象
    try:
        obj = _create_image_mobject(image_path)
    except Exception as e:
        logger.error(f"创建图像对象时出错: {e}")
        if not annotation:
            return

        # 如果图像创建失败但有注释，回退到仅显示注释
        logger.info("图像创建失败但有注释文本，将全屏显示注释。")
        try:
            text_obj = _animate_annotation_only(scene, annotation, target_rect, narration)
            scene.current_mobj = text_obj
        except Exception as e2:
            logger.error(f"处理注释时出错: {e2}")
        return

    # 5. 计算动画参数
    pan_params = _calculate_pan_animation_params(obj, target_rect)

    # 6. 播放动画
    result_obj = obj

    text_obj = _animate_zoom_and_pan(scene, obj, narration, pan_params, annotation)
    if text_obj:
        result_obj = Group(obj, text_obj)

    # 7. 更新区域内容
    scene.current_mobj = result_obj
    logger.info(f"显示图片完成: image_path={image_path}, id='{reference_id}'")
